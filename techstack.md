# Technology Stack

## Core Technologies
- **Python 3.9+** - Primary language across all projects
- **uv** - Modern Python package manager and environment management
- **Rich** - Terminal UI framework for enhanced CLI interfaces
- **Loguru** - Advanced logging with structured output

## Project Architecture
- **Modular CLI Applications** - Self-contained utilities with interactive interfaces
- **Configuration-driven** - YAML/TOML-based parameter management
- **Concurrent Processing** - ThreadPoolExecutor for parallel operations
- **Cross-platform Compatibility** - Windows batch scripts with fallback support

## Key Dependencies

### Media Processing (`py__VideoSplitter`)
- **FFmpeg/FFprobe** - Video manipulation and metadata extraction
- **PyYAML** - Configuration file parsing
- **Rich Progress** - Real-time operation feedback

### Content Downloaders
- **gallery-dl** - Pinterest content extraction
- **instaloader** - Instagram media downloading
- **pyperclip** - Clipboard integration

### File Management (`py__RenameWithEditor`)
- **filetype** - MIME type detection
- **xxhash** - Fast file hashing
- **tqdm** - Progress tracking

## Development Patterns
- **Dataclass-based Models** - Type-safe data structures
- **Exception-driven Flow** - Robust error handling with user feedback
- **Interactive Prompting** - Rich-based user input validation
- **Batch Processing** - Automated environment setup via `.bat` runners
