import argparse
import os
import subprocess
import sys
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

import yaml
from loguru import logger
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.prompt import Prompt, Confirm
from rich.table import Table

console = Console()

@dataclass
class VideoSegment:
    """Represents a single video segment."""
    name: str
    start: str
    end: str

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Split a video into segments using FFmpeg")
    parser.add_argument("-i", "--input_video", type=str, help="Path to input video file")
    parser.add_argument("-op", "--output_path", type=str, help="Output directory path")
    parser.add_argument("-tc", "--timecodes_file", type=str, help="Path to YAML/CSV timecodes file")
    parser.add_argument("--ffmpeg_path", default="ffmpeg", help="Path to ffmpeg executable")
    parser.add_argument("--ffprobe_path", default="ffprobe", help="Path to ffprobe executable")
    parser.add_argument("--output_format", default="mp4", help="Output video format")
    parser.add_argument("--codec", default="copy", help="Video codec (copy, libx264, etc.)")
    parser.add_argument("--threads", type=int, default=1, help="Number of concurrent threads")
    parser.add_argument("--after_effects_compatible", action="store_true", 
                        help="Re-encode for Adobe After Effects compatibility")
    parser.add_argument("--append_timestamps", action="store_true", default=True,
                        help="Append timestamps to output filenames")
    parser.add_argument("--include_source_prefix", action="store_true", default=False,
                        help="Include source filename as prefix")
    parser.add_argument("--use_subdirectory", action="store_true", default=False,
                        help="Create subdirectory for output files")
    parser.add_argument("--prompt", action="store_true", help="Interactive mode")
    return parser.parse_args()

def setup_logger():
    """Initialize logging with loguru."""
    logger.remove()
    logger.add(sys.stderr, level="INFO", format="{time} - {level} - {message}")
    logger.add("video_splitter.log", level="DEBUG", format="{time} - {level} - {message}")

def cleanup_logs():
    """Clean up log files after successful execution."""
    log_file = "video_splitter.log"
    logger.remove()  # Close logger to release file handle
    if os.path.exists(log_file):
        try:
            os.remove(log_file)
            console.print("[bold green]Successfully cleaned up log file[/bold green]")
        except Exception as e:
            console.print(f"[bold red]Error cleaning up log file: {str(e)}[/bold red]")

def wait_for_user_exit():
    """Wait for user input before exiting."""
    console.print("\n[dim]Press Enter to exit...[/dim]")
    input()

def get_video_duration(video_path: str, ffprobe_path: str = "ffprobe") -> float:
    """Get video duration using ffprobe."""
    cmd = [
        ffprobe_path, "-v", "error", "-show_entries", "format=duration",
        "-of", "default=noprint_wrappers=1:nokey=1", video_path
    ]
    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        return float(result.stdout.decode().strip())
    except subprocess.CalledProcessError as e:
        raise RuntimeError(f"Failed to get video duration: {e.stderr.decode().strip()}")

def load_timecodes_from_yaml(file_path: str) -> Dict[str, Any]:
    """Load configuration and segments from YAML file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f) or {}
    
    segments = []
    for seg in data.get('segments', []):
        if seg.get('start') and seg.get('end') and (seg.get('title') or seg.get('name')):
            segments.append((
                seg['start'].strip(),
                seg['end'].strip(), 
                (seg.get('title') or seg.get('name')).strip()
            ))
    
    return {
        'input_video': data.get('input_video'),
        'output_dir': data.get('output_dir'),
        'segments': segments,
        'ffmpeg_path': data.get('ffmpeg_path', 'ffmpeg'),
        'ffprobe_path': data.get('ffprobe_path', 'ffprobe'),
        'output_format': data.get('output_format', 'mp4'),
        'codec': data.get('codec', 'copy'),
        'threads': data.get('threads', 1),
        'after_effects_compatible': data.get('after_effects_compatible', False),
        'append_timestamps': data.get('append_timestamps', True),
        'include_source_prefix': data.get('include_source_prefix', False),
        'use_subdirectory': data.get('use_subdirectory', False)
    }

def prompt_for_segments(video_duration: float) -> List[Tuple[str, str, str]]:
    """Interactively prompt user for video segments."""
    console.print(f"\n[bold cyan]Video Duration:[/bold cyan] {video_duration:.1f} seconds")
    console.print("[bold cyan]Enter video segments:[/bold cyan]\n")
    
    segments = []
    segment_num = 1
    
    while True:
        name = Prompt.ask(f"[bold cyan]Segment {segment_num} name[/bold cyan]", 
                         default=f"segment_{segment_num}")
        start = Prompt.ask("[bold cyan]Start time (HH:MM:SS or MM:SS)[/bold cyan]")
        end = Prompt.ask("[bold cyan]End time (HH:MM:SS or MM:SS)[/bold cyan]")
        
        segments.append((start, end, name))
        console.print(f"[bold green]Added '{name}': {start} to {end}[/bold green]\n")
        
        if not Confirm.ask("[bold cyan]Add another segment?[/bold cyan]", default=False):
            break
        segment_num += 1
    
    return segments

def get_user_inputs(args):
    """Get and validate user inputs, with interactive prompts if needed."""
    yaml_config = {}
    
    # Load YAML configuration if provided
    if args.timecodes_file and Path(args.timecodes_file).is_file():
        try:
            yaml_config = load_timecodes_from_yaml(args.timecodes_file)
            console.print(f"[bold green]Loaded configuration from {args.timecodes_file}[/bold green]")
        except Exception as e:
            console.print(f"[bold red]Error loading YAML file: {e}[/bold red]")
    
    # Prompt for missing inputs or if in interactive mode
    if args.prompt or not args.input_video:
        default_video = yaml_config.get('input_video') or args.input_video or ""
        args.input_video = Prompt.ask("[bold cyan]Input video file path[/bold cyan]", 
                                     default=default_video)
    
    if not args.input_video or not Path(args.input_video).is_file():
        console.print("[bold red]Error: Input video file not found[/bold red]")
        sys.exit(1)
    
    if args.prompt or not args.output_path:
        default_output = yaml_config.get('output_dir') or args.output_path
        if not default_output:
            default_output = str(Path(args.input_video).parent) if args.input_video else os.getcwd()
        args.output_path = Prompt.ask("[bold cyan]Output directory[/bold cyan]", 
                                     default=default_output)
    
    # Get segments
    segments = yaml_config.get('segments', [])
    if args.prompt or not segments:
        if segments and not args.prompt:
            console.print(f"[bold green]Found {len(segments)} segments in configuration[/bold green]")
        else:
            try:
                duration = get_video_duration(args.input_video, 
                                            yaml_config.get('ffprobe_path', args.ffprobe_path))
                segments = prompt_for_segments(duration)
            except Exception as e:
                console.print(f"[bold red]Error getting video duration: {e}[/bold red]")
                sys.exit(1)
    
    # Merge YAML config with CLI args (CLI takes precedence)
    for key, value in yaml_config.items():
        if not hasattr(args, key) or getattr(args, key) is None or getattr(args, key) == argparse.SUPPRESS:
            setattr(args, key, value)
    
    return args.input_video, args.output_path, segments, args

def split_video_segment(input_video: str, segment: VideoSegment, output_dir: str, 
                       ffmpeg_path: str, output_format: str, codec: str,
                       append_timestamps: bool, include_source_prefix: bool,
                       after_effects_compatible: bool) -> Tuple[str, Optional[str], Optional[str]]:
    """Split a single video segment using FFmpeg."""
    source_name = Path(input_video).stem if include_source_prefix else ""
    prefix = f"{source_name}_" if include_source_prefix else ""
    
    if append_timestamps:
        start_clean = segment.start.replace(':', '-')
        end_clean = segment.end.replace(':', '-')
        filename = f"{prefix}{segment.name}_{start_clean}_to_{end_clean}.{output_format}"
    else:
        filename = f"{prefix}{segment.name}.{output_format}"
    
    output_path = Path(output_dir) / filename
    
    if after_effects_compatible:
        cmd = [
            ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end,
            "-c:v", "libx264", "-preset", "faster", "-pix_fmt", "yuv420p",
            "-c:a", "aac", "-b:a", "192k", "-movflags", "+faststart", "-y", str(output_path)
        ]
    else:
        cmd = [
            ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end,
            "-c", codec, "-y", str(output_path)
        ]
    
    try:
        subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return segment.name, str(output_path), None
    except subprocess.CalledProcessError as e:
        return segment.name, None, e.stderr.decode().strip()


def process_video_segments(input_video: str, output_dir: str,
                           segments: List[Tuple[str, str, str]],
                           config: argparse.Namespace) -> List[Tuple[str, str]]:
    """Process all video segments with progress tracking."""
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    segment_objects = [VideoSegment(name, start, end)
                       for start, end, name in segments]
    results = []

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console,
        transient=True
    ) as progress:
        task = progress.add_task(
            "[bold cyan]Processing video segments...[/bold cyan]",
            total=len(segment_objects)
        )

        with ThreadPoolExecutor(max_workers=config.threads) as executor:
            future_to_segment = {
                executor.submit(
                    split_video_segment,
                    input_video,
                    segment,
                    output_dir,
                    config.ffmpeg_path,
                    config.output_format,
                    config.codec,
                    config.append_timestamps,
                    config.include_source_prefix,
                    config.after_effects_compatible
                ): segment.name for segment in segment_objects
            }

            for future in as_completed(future_to_segment):
                segment_name = future_to_segment[future]
                try:
                    name, output_path, error = future.result()
                    if output_path:
                        results.append((name, output_path))
                        console.print(f"[bold green]✓ Completed: {name}[/bold green]")
                    else:
                        results.append((name, f"Failed: {error}"))
                        console.print(f"[bold red]✗ Failed: {name}[/bold red]")
                except Exception as e:
                    error_msg = f"Exception: {str(e)}"
                    results.append((segment_name, error_msg))
                    console.print(f"[bold red]✗ Exception: {segment_name}[/bold red]")
                finally:
                    progress.advance(task)

    return results

def display_summary(input_video: str, output_dir: str,
                    segments: List[Tuple[str, str, str]],
                    config: argparse.Namespace):
    """Display operation summary before processing."""
    table = Table(title="Video Splitting Configuration",
                  show_header=False, box=None)
    table.add_column("Parameter", style="bold cyan")
    table.add_column("Value", style="dim")

    table.add_row("Input Video", input_video)
    table.add_row("Output Directory", output_dir)
    table.add_row("Output Format", config.output_format)
    table.add_row("Codec", config.codec)
    table.add_row("Threads", str(config.threads))
    ae_compat = "Yes" if config.after_effects_compatible else "No"
    table.add_row("AE Compatible", ae_compat)
    append_ts = "Yes" if config.append_timestamps else "No"
    table.add_row("Append Timestamps", append_ts)
    include_prefix = "Yes" if config.include_source_prefix else "No"
    table.add_row("Include Source Prefix", include_prefix)
    table.add_row("Segments", str(len(segments)))

    console.print("\n[bold blue]--- Operation Summary ---[/bold blue]")
    console.print(Panel(table, border_style="blue"))

    if segments:
        console.print("\n[bold cyan]Segments to process:[/bold cyan]")
        seg_table = Table(show_header=True, header_style="bold magenta")
        seg_table.add_column("Name", style="cyan")
        seg_table.add_column("Start", style="green")
        seg_table.add_column("End", style="green")

        for start, end, name in segments:
            seg_table.add_row(name, start, end)
        console.print(seg_table)


def display_results(results: List[Tuple[str, str]]):
    """Display processing results."""
    table = Table(title="Processing Results", show_header=True,
                  header_style="bold cyan")
    table.add_column("Segment", style="cyan")
    table.add_column("Status", style="dim")
    table.add_column("Output", style="dim")

    for name, result in results:
        if result.startswith("Failed") or result.startswith("Exception"):
            table.add_row(name, "[bold red]Failed[/bold red]", result)
        else:
            table.add_row(name, "[bold green]Success[/bold green]", result)

    console.print("\n")
    console.print(table)


def main():
    """Main entry point."""
    args = parse_arguments()
    input_video, output_path, segments, config = get_user_inputs(args)

    setup_logger()

    try:
        # Display summary and get confirmation
        display_summary(input_video, output_path, segments, config)

        proceed_msg = "\n[bold cyan]Proceed with video splitting?[/bold cyan]"
        if not Confirm.ask(proceed_msg, default=True):
            console.print("[yellow]Operation cancelled by user[/yellow]")
            return

        # Process video segments
        console.print("\n[bold cyan]Starting video processing...[/bold cyan]")
        results = process_video_segments(input_video, output_path,
                                         segments, config)

        # Display results
        display_results(results)

        # Check if all segments were successful
        def is_failed(result):
            return (result.startswith("Failed") or
                    result.startswith("Exception"))

        successful = all(not is_failed(result) for _, result in results)

        if successful:
            success_msg = ("\n[bold green]All segments processed "
                           "successfully![/bold green]")
            console.print(success_msg)
            cleanup_logs()
        else:
            fail_msg = ("\n[bold yellow]Some segments failed to process. "
                        "Check the results above.[/bold yellow]")
            console.print(fail_msg)

    except Exception as e:
        console.print(f"\n[bold red]Error:[/bold red] {e}")
        logger.exception(f"Main execution error: {e}")
    finally:
        console.print("\n[bold green]Video splitting completed.[/bold green]")
        wait_for_user_exit()


if __name__ == "__main__":
    main()
